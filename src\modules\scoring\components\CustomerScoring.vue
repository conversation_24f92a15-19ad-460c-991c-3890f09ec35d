<template>
  <div v-if="loading" class="p-4 text-center">Carregant dades de scoring...</div>
  <template v-else>
    <CardComponent
      icon="thermometer"
      title="PUNTUACIÓ CLIENT"
      :tab-actions="tabs"
      @action="handleAction"
      :disableButton="!requiresManualApproval"
    >
   
      <div v-if="hasScoringResult">
        <div class="px-12">
          <div class="grid grid-cols-3 gap-4 pt-5 pb-5">
            <div>
              <p>Qualificació</p>
              <div
                class="w-28 h-6 rounded-l-full rounded-r-full px-8 flex items-center justify-center"
                :class="gradeClasses.bgClass"
              >
                <span :class="gradeClasses.textClass">{{ currentGrade }}</span>
              </div>
            </div>

            <ScoringInfoItem label="Probabilitat deute" isPercent :value="scoringProbability" />
            <ScoringInfoItem label="Data scoring" isDate :value="scoringDate" />
          </div>

          <div v-if="!requiresManualApproval" class="grid grid-cols-3 gap-4 pt-5 pb-5">
            <ScoringInfoItem label="Aprovat per" :value="scoringApprovedBy" />
            <ScoringInfoItem label="Data aprovació" isDate :value="approvalDate" />
          </div>
        </div>
        <hr class="border-t border-gray-300 dark:border-gray-light w-[95%] mx-auto" />
        <GradeLegend :grades="GRADES" :current-grade="currentGrade" />
      </div>

      <div v-if="hasDebtResult">
        <div class="px-12">
          <div class="grid grid-cols-4 gap-4 pt-5 pb-5">
            <ScoringInfoItem label="Deute" isPrice :value="debtAmount" />
            <ScoringInfoItem label="Data actualització deute" isDate :value="debtDate" />

            <template v-if="!requiresManualApproval">
              <ScoringInfoItem label="Aprovat per" :value="scoringApprovedBy" />
              <ScoringInfoItem label="Data aprovació" isDate :value="approvalDate" />
            </template>
          </div>
        </div>
      </div>
    </CardComponent>
  </template>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue'
import CardComponent from '@/components/card/cardComponent.vue'
import ScoringInfoItem from './ScoringInfoItem.vue'
import GradeLegend from './GradeLegend.vue'
import type { IResultItemScoring } from '@/services/api/scoring/interfaces/scoring-value.interface'

interface Props {
  scoringData?: IResultItemScoring | null
  loading: boolean
  error?: Error | null
  validationResult: boolean | null 
}

const props = defineProps<Props>()

const { scoringData, loading, error } = toRefs(props)

const tabs = computed(() => {
  return [
    {
      label: manualAccepted.value ? ' Venda validada' : 'Validar última venda',
      value: 'VALIDATION',
      disabled: manualAccepted.value,
      active: true,
      icon: manualAccepted.value ? 'circle-check' : 'wand-magic-sparkles'
    }
  ]
})

const GRADES = ['AAA', 'AA', 'A', 'B', 'BB', 'BBB'] as string[]
type GradeType = (typeof GRADES)[number]

const manualAccepted = computed(() => {
  const result = scoringData.value
  return (
    result?.scoringResult?.acceptanceResult?.manualAcceptation === true ||
    result?.debtResult?.acceptanceResult?.manualAcceptation === true
  )
})

const currentGrade = computed(() => scoringData.value?.scoringResult?.scoring ?? '')
const hasScoringResult = computed(() => Boolean(scoringData.value?.scoringResult))
const hasDebtResult = computed(
  () => !hasScoringResult.value && Boolean(scoringData.value?.debtResult)
)

const scoringProbability = computed(
  () => scoringData.value?.scoringResult?.defaultProbability || ''
)

const scoringDate = computed(() => scoringData.value?.scoringResult?.date || '')
const scoringApprovedBy = computed(() => {
  return (
    scoringData.value?.scoringResult?.acceptanceResult?.acceptedByUserName ||
    scoringData.value?.debtResult?.acceptanceResult?.acceptedByUserName ||
    ''
  )
})

const approvalDate = computed(() => {
  return (
    scoringData.value?.scoringResult?.acceptanceResult?.acceptanceDate ||
    scoringData.value?.debtResult?.acceptanceResult?.acceptanceDate ||
    ''
  )
})

const debtAmount = computed(() => scoringData.value?.debtResult?.debtAmount || '')
const debtDate = computed(() => scoringData.value?.debtResult?.latestUpdate || '')
const requiresManualApproval = computed(() => props.validationResult !== true)

const gradeClasses = computed(() => {
  const grade = currentGrade.value?.toLowerCase() as Lowercase<GradeType> | undefined
  return grade
    ? { bgClass: `bg-scoring-bg-${grade}`, textClass: `text-scoring-text-${grade}` }
    : { bgClass: '', textClass: '' }
})

const emit = defineEmits<{
  (e: 'validate'): void
}>()

const handleAction =  () => {
  emit('validate')
} 
</script>
