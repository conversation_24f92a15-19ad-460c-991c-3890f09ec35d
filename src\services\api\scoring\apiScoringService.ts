import axios from 'axios'
import {
  X_PARLEM_APIKEY,
  CONTENT_TYPE,
  APPLICATION_JSON,
  API
} from '../../constants/services.constants'
import type { IApiRes, IHeaders } from '@/services/interfaces'
import {
  DOCUMENT,
  LEADS,
  SCORE,
  SCORING,
  SELLS,
  VALIDATE
} from './constants/scoringService.constants'
import type { IApiScoring } from './interfaces/api-scoring.interface'
import type { IApiResponseScoring } from './interfaces/scoring-value.interface'

const scoringHeader: IHeaders = {
  headers: {
    [X_PARLEM_APIKEY]: import.meta.env.VITE_API_KEY_SCORING,
    [CONTENT_TYPE]: APPLICATION_JSON
  }
}

const apiScoringService: IApiScoring = {
  //
  async getCustomerScoring(documentNumber: string) {
    const url: string = `${
      import.meta.env.VITE_BASE_URL
    }/${SCORING}/${API}/${LEADS}/${DOCUMENT}/${documentNumber}/${SCORE}`
    try {
      const response: IApiRes<IApiResponseScoring> = await axios.get(url, scoringHeader as IHeaders)
      return response.data
    } catch (error: any) {
      console.log('ERROR', error)
      throw error
    }
  },

  async getLastSaleValidation(sellId: string) {
    const url: string = `${
      import.meta.env.VITE_BASE_URL
    }/${SCORING}/${API}/${SELLS}/${sellId}/${VALIDATE}`
    try {
      const response: IApiRes<IApiResponseScoring> = await axios.patch(url, {}, scoringHeader as IHeaders)
      const data = response.data

      if (!data.isSuccess) {
        const reason = data.errors?.[0]?.reason || 'Error desconegut en validar la venda.'
        throw new Error(reason)
      }

      return data
    } catch (error: any) {
      console.error('ERROR', error)
      throw error
    }
  }
}

export default apiScoringService
