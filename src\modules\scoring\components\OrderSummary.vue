<template>
  <div v-if="loading" class="p-4 text-center">Carregant dades de scoring...</div>

  <template v-else>
    <CardComponent icon="basket-shopping" title="DETALL DE LA ÚLTIMA VENDA" :tabs="tabs" disableButton>
      <div class="flex justify-between pt-5 pb-3 px-12" v-if="sellingDetails">
        <ScoringInfoItem label="Id de la venda" :value="sellingDetails?.id" />
        <ScoringInfoItem label="RGU" :value="sellingDetails?.rgu" />
        <ScoringInfoItem
          label="Preu total venda"
          :value="sellingDetails?.totalPrice.toString()"
          isPrice
        />
        <ScoringInfoItem label="Agent" :value="sellingDetails?.agent" />
        <ScoringInfoItem label="Canal" :value="sellingDetails?.channel" />
      </div>

      <div class="mt-4 mb-4 px-12">
        <Sales
          :itemsByProps="rowData"
          :columns="sellingDetailsColumns"
          :webComponentType="'sellingDetails'"
        />
      </div>
    </CardComponent>
  </template>
</template>

<script setup lang="ts">
import { toRefs, computed } from 'vue'
import CardComponent from '@/components/card/cardComponent.vue'
import Sales from '@/components/sales/Sales.vue'
import ScoringInfoItem from './ScoringInfoItem.vue'

import getColumnFields from '@/utils/columns/index'
import type {
  IResultItemSelling,
  ContractedProduct
} from '@/services/api/selling/interfaces/selling-value.interface'
import sellingDetailsColumnsKeys from '../utils/sellingDetailsColumnsKeys'
import { setAddressString } from 'parlem-webcomponents-common'

interface Props {
  sellingDetails?: IResultItemSelling | null
  loading: boolean
  error?: Error | null
}

const props = defineProps<Props>()

const { sellingDetails, loading, error } = toRefs(props)

const sellingDetailsColumns = getColumnFields(sellingDetailsColumnsKeys)

const tabs = [
  {
    active: false
  }
]

const rowData = computed(() => {
  if (!sellingDetails.value || !sellingDetails.value.contractedRates) return []

  const iconMap: Record<string, string> = {
    fibra: 'wifi',
    movil: 'smartphone',
    tv: 'tv',
    switchBoard: 'microchip',
    fixe: 'phone',
    digitalService: 'cloud'
  }

  const normalizeKey = (key: string) => key.toLowerCase()

  return sellingDetails.value.contractedRates.map((rate, index) => ({
    index: (index + 1).toString(),
    state: 'Pending',
    rateName: rate.rateName,
    contractedProducts: rate.contractedProducts.map((product: ContractedProduct) => {
      let icon = 'box'
      let formattedAddress = ''

      const fiberEntry = Object.entries(product).find(([key]) => normalizeKey(key) === 'fiber')
      if (fiberEntry) {
        icon = iconMap.fibra
        const fiberObj = fiberEntry[1] as any
        if (fiberObj.installationAddress) {
          formattedAddress = setAddressString(fiberObj.installationAddress)
        }
      } else {
        const productKeys = Object.keys(product).map(normalizeKey)

        if (productKeys.includes('mobile')) icon = iconMap.movil
        else if (productKeys.includes('tv')) icon = iconMap.tv
        else if (productKeys.includes('switchboard')) icon = iconMap.switchBoard
        else if (productKeys.includes('landline')) icon = iconMap.fixe
        else if (productKeys.includes('digitalservice')) icon = iconMap.digitalService
      }

      return {
        icon,
        name: product.provisioningClass,
        description: formattedAddress ? formattedAddress : product.productName,
      }
    }),
    preu: rate.price
  }))
})

</script>
