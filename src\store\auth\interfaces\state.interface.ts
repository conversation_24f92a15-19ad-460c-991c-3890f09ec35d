import type { IUserInfo } from '@/services/api/shoppingcart/interfaces/user-info.interface'
import type { IAccount } from './account.interface'
import type { IPublicClientApplication } from '@azure/msal-browser'

export interface IAuthState {
  msalInstance?: IPublicClientApplication
  accessToken: string
  account: IAccount | undefined | null
  authenticationError: boolean
  userInfo: IUserInfo | null
  company: string
  language: string
}
