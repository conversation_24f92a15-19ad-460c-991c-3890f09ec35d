<template>
  <CardComponent icon="list" title="LLISTAT DE VENDES" disableButton>
    <div class="py-5 px-12">
      <div ref="pwSellerSells"></div>
    </div>
  </CardComponent>
</template>
<script setup lang="ts">
import CardComponent from '@/components/card/cardComponent.vue'
import { onMounted, ref } from 'vue'

const props = defineProps({
  lang: {
    type: String,
    default: 'ca'
  },
  userInfo: {
    type: String,
    default: null
  },
  company: {
    type: String,
    default: null
  },
  documentNumber: {
    type: String,
    required: true
  },
  isManualActions: {
    type: Boolean
  }
})

const pwSellerSells = ref<HTMLElement | null>(null)

const loadSellerSells = (): void => {
  const sellerSells = document.createElement('pw-seller-sells')
  const sellObject = {
    documentNumber: props.documentNumber,
  }
  sellerSells.setAttribute('lang', props.lang)
  sellerSells.setAttribute('company', props.company)
  sellerSells.setAttribute('sell-data', JSON.stringify(sellObject))
  sellerSells.setAttribute('is-manual-actions', props.isManualActions.toString())

  if (pwSellerSells.value) {
    pwSellerSells.value.innerHTML = ''
    pwSellerSells.value.appendChild(sellerSells)
  }
}

onMounted(() => {
  loadSellerSells()
})
</script>
