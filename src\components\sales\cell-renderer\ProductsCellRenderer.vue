<template>
  <div class="flex flex-col gap-2">
    <div v-for="(product, index) in products" :key="index" class="flex items-center gap-2">
      <div class="w-[12px]">
        <font-awesome-icon :icon="product.icon" class="w-full" />
      </div>
      <span class="">{{ product.name }}</span> 
      <span class=""> - {{ product.description }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{ params: { value: any } }>()

const products = computed(() => {
  if (!props.params?.value) return []
  return Array.isArray(props.params.value) ? props.params.value : [props.params.value]
})
</script>
