import apiCatalogService from '@/services/api/catalog/apiCatalogService'
import apiScoringService from '@/services/api/scoring/apiScoringService'
import apiManualactionsService from '@/services/api/manualactions/apiManualactionsService'

import type { IPicklist } from '@/services/api/catalog/interfaces/picklist.interface'
import type { IPicklistManualActions } from '@/services/api/manualactions/interfaces/picklist.interface'
import type { IManualActionsStore } from '@/store/manual-actions/interfaces/store.interface'
import type { ISearchFilter } from '@/utils/filters/interface/searchFilters.interface'
import type { ISelectedFilter } from '@/utils/filters/interface/selectedFilter.interface'
import apiSellingService from '@/services/api/selling/apiSellingService'
import { getTypeKeyByValue } from './../../utils/manualactions/getTypeKeyByValue'
import { getReasonValueByType } from './../../utils/manualactions/getReasonValueByType'

///_______GET PICKLISTS & hierarchicalpicklists_________
export async function getPicklists(this: IManualActionsStore): Promise<void> {
  const picklists: IPicklist[] = await apiCatalogService.getPicklists()
  if (picklists?.length) {
    this.picklists = [...this.picklists, ...picklists]
  }
}

export async function getHierarchicaPicklists(this: IManualActionsStore): Promise<void> {
  const hierarchicalPicklists: IPicklist[] = await apiCatalogService.getHierarchicaPicklists()
  if (hierarchicalPicklists?.length) {
    this.picklists = [...this.picklists, ...hierarchicalPicklists]
  }
}

export async function getPicklistsManualActions(
  this: IManualActionsStore,
  languageId: string
): Promise<void> {
  const picklistsManualActions: IPicklistManualActions[] =
    await apiManualactionsService.getPicklists(languageId)
  if (picklistsManualActions?.length) {
    this.picklistsManualActions = [...this.picklistsManualActions, ...picklistsManualActions]
  }
}

///______GET FILTERS________
export function setSearchFilters(this: IManualActionsStore, searchFilters: ISearchFilter[]) {
  this.searchFilters = searchFilters
}

export function getFilterOptions(this: IManualActionsStore): void {
  const firstFilterOptions: ISearchFilter[] = this.searchFilters
  if (this.picklistsManualActions.length) {
    firstFilterOptions.forEach((filter: ISearchFilter) => {
      const optionSelected: IPicklist | undefined = this.picklists.find(
        (option: IPicklist) =>
          option.name.toLowerCase() === `${filter.key ? filter.key : filter.value}`.toLowerCase()
      )

      if (optionSelected !== undefined) {
        filter.options = optionSelected.values
      }
    })
  }
}

export function setSearchParams(this: IManualActionsStore, params: any): void {
  this.searchParams = { ...this.searchParams, ...params }
}

///_______GET SCORING __________________
export async function fetchCustomerScoring(this: IManualActionsStore, param: any) {
  try {
    const response = await apiScoringService.getCustomerScoring(param)
    this.customerScoring = response
    return response.result[0]
  } catch (error) {
    console.log(error)
    throw error
  }
}

export async function fetchSalesValidation(this: IManualActionsStore, param: any) {
  try {
    const isSaleValid = await apiScoringService.getLastSaleValidation(param.value)
    this.lastSaleValidation = isSaleValid
    return isSaleValid
  } catch (error) {
    console.log(error)
    throw error
  }
}

export async function fetchCustomerSellingsDetail(this: IManualActionsStore, param: string) {
  try {
    const sellingDetails = await apiSellingService.getCustomerSellingDetail(param)
    this.customerSellingDetail = sellingDetails
    return sellingDetails.result
  } catch (error) {
    
  }
}

///_______MANUAL ACTIONS __________________

export async function executeManualAction(
  this: IManualActionsStore,
  manualActionsid: string,
  body: object,
  type: string
) {
  try {
    const response = await apiManualactionsService.executeManualAction(manualActionsid, body)

    if (response?.isSuccess) {
      const searchParams = {
        ...this.searchParams,
        // hardcoded
        provider: 'Webdealer',
        type: getReasonValueByType(type)
      }
      await this.getManualActionsBySearch(searchParams)
    }

    return response
  } catch (error) {
    console.error('Error executing manual action:', error)
    throw new Error('Failed to execute manual action')
  }
}

export async function getManualActionsBySearch(
  this: IManualActionsStore,
  searchParams: Record<string, any>
): Promise<void> {
  const response = await apiManualactionsService.searchManualActions(searchParams)

  if (response?.isSuccess && Array.isArray(response.result)) {
    const apiType = searchParams.type
    const storeKey = getTypeKeyByValue(apiType)

    if (!storeKey) return

    if (!this.items[storeKey]) {
      this.items[storeKey] = []
    }

    this.items[storeKey] = response.result
  } else {
    console.error('Error en getManualActionsBySearch:', response?.errors)
  }
}

export function showPopup(
  this: IManualActionsStore,
  payload: {
    type: 'cancel' | 'migrate' | 'close'
    rowData: any
  }
): void {
  this.popup = {
    isVisible: true,
    type: payload.type,
    rowData: payload.rowData
  }
}

export function hidePopup(this: IManualActionsStore): void {
  this.popup = {
    isVisible: false,
    type: null,
    rowData: null
  }
}
