<template>
  <div class="flex items-center gap-2 text-sm font-semibold !my-2">
    <div
      class="h-[26px] min-w-[75px] rounded-full flex items-center justify-center px-4 cursor-pointer bg-primary text-white"
      @click="handleAction"
      v-if="!isDisabled"
    >
      <span class="font-bold">{{ $t('actions.start') }}</span>
    </div>

    <div v-if="isDisabled" class="flex items-center gap-1 text-gray">
      <font-awesome-icon icon="fa-solid fa-circle-check" class="w-5 mr-1" />
      <span>{{ $t(completedStateLabel) }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useManualActions } from '@/store/manual-actions'

const { t } = useI18n()
const manualActionsStore = useManualActions()

const props = defineProps({
  params: {
    type: Object,
    required: true
  }
})

const isDisabled = computed(() => props.params.data.state === 'Done')


const completedStateLabel = computed(() => {
  switch (props.params.type) {
    case 'cancel':
      return 'actions.cancel.completed'
    case 'migrate':
      return 'actions.migrate.completed'
    case 'close':
    default:
      return 'actions.close.completed'
  }
})

const handleAction = () => {
  if (!isDisabled.value) {
    manualActionsStore.showPopup({
      type: props.params.data.type,
      rowData: props.params.data
    })
  }
}
</script>
