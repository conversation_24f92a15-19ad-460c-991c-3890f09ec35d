import StateCellRenderer from '@/components/list/cell-renderer/StateCellRenderer.vue'
import ActionsCellRenderer from '@/components/list/cell-renderer/ActionsCellRenderer.vue'
import DateCellRenderer from '@/components/list/cell-renderer/DateCellRenderer.vue'
import ProductsCellRenderer from '@/components/sales/cell-renderer/ProductsCellRenderer.vue'
import DefaultCellRenderer from '@/components/list/cell-renderer/DefaultCellRenderer.vue'
import { i18n } from '@/i18n'
import PreuCellRenderer from '@/components/sales/cell-renderer/PreuCellRenderer.vue'

const columnFieldOptions: any = [
  {
    field: 'id',
    headerName: i18n.t('columns.id'),
    minWidth: 200,
    hide: true,
    context: { key: 'id' }
  },
  {
    field: 'state',
    headerName: i18n.t('columns.state'),
    minWidth: 120,
    cellRenderer: StateCellRenderer,
    context: { key: 'state' }
  },
  {
    field: 'Accions',
    headerName: i18n.t('columns.actions'),
    cellRenderer: ActionsCellRenderer,
    filter: false,
    sortable: false,
    minWidth: 300,
    context: { key: 'Accions' }
  },
  {
    field: 'creationDate',
    headerName: i18n.t('columns.creationDate'),
    cellRenderer: DateCellRenderer,
    minWidth: 100,
    context: { key: 'creationDate' }
  },
  {
    field: 'executionDate',
    headerName: i18n.t('columns.executionDate'),
    cellRenderer: DateCellRenderer,
    minWidth: 100,
    context: { key: 'executionDate' }
  },
  {
    field: 'rateName',
    headerName: i18n.t('columns.rate'),
    cellRenderer: DefaultCellRenderer,
    context: { key: 'rateName' }
  },
  {
    field: 'contractedProducts',
    headerName: i18n.t('columns.products'),
    cellRenderer: ProductsCellRenderer,
    context: { key: 'contractedProducts' }
  },
  {
    field: 'preu',
    headerName: i18n.t('columns.price'),
    maxWidth: 150,
    context: { key: 'preu' },
    cellRenderer: PreuCellRenderer
  },
  {
    field: 'index',
    headerName: '#',
    maxWidth: 50,
    cellRenderer: DefaultCellRenderer,
    context: { key: 'index' }
  },
  {
    field: 'type',
    headerName: i18n.t('columns.type'),
    hide: true,
    cellRenderer: DefaultCellRenderer,
    context: { key: 'type' }
  },
  {
    field: 'provider',
    headerName: i18n.t('columns.provider'),
    minWidth: 120,
    cellRenderer: DefaultCellRenderer,
    context: { key: 'provider' }
  },
  {
    field: 'customerInfo.name',
    headerName: i18n.t('columns.customerName'),
    filter: 'agTextColumnFilter',
    minWidth: 180,
    cellRenderer: DefaultCellRenderer,
    context: { key: 'customerName' }
  },
  {
    field: 'customerInfo.documentNumber',
    headerName: i18n.t('columns.documentNumber'),
    filter: 'agTextColumnFilter',
    minWidth: 120,
    cellRenderer: DefaultCellRenderer,
    context: { key: 'documentNumber' }
  },
  {
    field: 'customerInfo.contactPhone',
    headerName: i18n.t('columns.contactPhone'),
    filter: 'agTextColumnFilter',
    minWidth: 120,
    cellRenderer: DefaultCellRenderer,
    context: { key: 'contactPhone' }
  },
  {
    field: 'closeAction.productName',
    headerName: i18n.t('columns.product'),
    filter: 'agTextColumnFilter',
    minWidth: 200,
    cellRenderer: DefaultCellRenderer,
    context: { key: 'productName' }
  },
  {
    field: 'closeAction.rateName',
    headerName: i18n.t('columns.rate'),
    filter: 'agTextColumnFilter',
    minWidth: 200,
    cellRenderer: DefaultCellRenderer,
    context: { key: 'rateName' }
  },
  {
    field: 'closeAction.fiber.number',
    headerName: i18n.t('columns.fiberNumber'),
    filter: 'agTextColumnFilter',
    minWidth: 120,
    cellRenderer: DefaultCellRenderer,
    context: { key: 'fiberNumber' }
  },
  {
    field: 'executionUsername',
    headerName: i18n.t('columns.user'),
    filter: 'agTextColumnFilter',
    minWidth: 120,
    cellRenderer: DefaultCellRenderer,
    context: { key: 'executionUsername' }
  }
]

export default function getColumnFields(keys: string[]): any {
  return keys.map((key) => {
    const column = columnFieldOptions.find((c: any) => c.context?.key === key)
    return column ? { ...column } : { 
      field: key, 
      headerName: i18n.t(`columns.${key}`), 
      context: { key },
      cellRenderer: DefaultCellRenderer 
    }
  })
}
