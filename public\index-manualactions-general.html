<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Manual Actions</title>
    <script
      id="wc-manualactions"
      src="https://dev-pwc.parlem.com/manualactions/parlem-webcomponents-manualactions.umd.js"
    ></script>
  </head>
  <body>
    <div id="pw-manual-actions"></div>
  </body>
  <script>
    const queryString = window.location.search
    const urlParams = new URLSearchParams(queryString)
    const lang = urlParams.get('lang') || 'ca'
    const company = urlParams.get('company') || 'Parlem'
    const type = urlParams.get('type') || 'close' // pot ser 'close', 'cancel' o 'migrate'

    // Load webcomponent with dynamic params
    let manualActions = document.createElement('pw-manual-actions')
    manualActions.setAttribute('lang', lang)
    manualActions.setAttribute('company', company)
    manualActions.setAttribute('type', type)

    const pwManualActions = document.getElementById('pw-manual-actions')
    pwManualActions.appendChild(manualActions)
  </script>
</html>