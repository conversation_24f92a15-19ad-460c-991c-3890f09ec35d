{
  "home": {
    "header": "Welcome to the Vue 3 I18n!"
  },
  "actions": {
    "start": "Start action",
    "cancel": {
      "start": "Start Cancellation",
      "completed": "Cancelled",
      "confirmTitle": "Cancellation Confirmation",
      "confirmDescription": "Are you sure you want to cancel this item?",
      "successMessage": "Successfully cancelled",
      "errorMessage": "Error cancelling",
      "unexpectedError": "Unexpected error while cancelling"
    },
    "migrate": {
      "start": "Start Migration",
      "completed": "Migrated",
      "confirmTitle": "Migration Confirmation",
      "confirmDescription": "Are you sure you want to migrate this item?",
      "successMessage": "Successfully migrated",
      "errorMessage": "Error migrating",
      "unexpectedError": "Unexpected error while migrating"
    },
    "close": {
      "start": "Start Termination",
      "completed": "Terminated",
      "confirmTitle": "Termination Confirmation",
      "confirmDescription": "Are you sure you want to terminate this item?",
      "successMessage": "Successfully terminated",
      "errorMessage": "Error terminating",
      "unexpectedError": "Unexpected error while terminating"
    }
  },
  "errors": {
    "noResults": "No results found",
  },
  "pagination": {
    "listCount": "The list contains {count} {elements}",
    "element": "item",
    "elements": "items",
    "previousPage": "Previous page",
    "nextPage": "Next page"
  },
  "columns": {
    "id": "Id",
    "state": "State",
    "actions": "Actions",
    "creationDate": "Creation Date",
    "executionDate": "Execution Date",
    "rate": "Rate",
    "products": "Products",
    "price": "Price",
    "type": "Type",
    "provider": "Provider",
    "customer": "Customer",
    "product": "Product",
    "fiberNumber": "Fiber Number",
    "user": "User",
    "customerName": "Customer name",
    "documentNumber": "Document",
    "contactPhone": "Contact phone"
  },
  "filters": {
    "documentNumber": "Document number",
    "provider": "Provider",
    "state": "State",
    "manualActionType": "Manual Action Type",
    "productClass": "Product Class",
    "productSubclass": "Product Subclass",
    "status": "Status",
    "subscriptionId": "Subscription ID",
    "workOrder": "Work Order"
  },
  "popup": {
    "stateLabel": "State",
    "statePlaceholder": "Select a state",
    "reasonLabel": "Reason",
    "reasonPlaceholder": "Specify the reason",
    "cancelButton": "No, cancel",
    "acceptButton": "Yes, accept"
  }
}
