{"name": "parlem-webcomponents-manualactions", "version": "1.0.30", "description": "PWC Manualactions", "homepage": "https://<EMAIL>/ParlemTelecom/Parlem%20WebComponents/_git/parlem-webcomponents-manualactions", "main": "./dist/parlem-webcomponents-manualactions.umd.js", "module": "./dist/parlem-webcomponents-manualactions.es.js", "exports": {".": {"import": "./dist/parlem-webcomponents-manualactions.es.js", "require": "./dist/parlem-webcomponents-manualactions.umd.js"}}, "files": ["dist/*"], "scripts": {"dev": "vite --force true", "staging": "vite --mode staging --force true", "build": "run-p type-check build:prod", "preview": "vite preview", "test:unit": "vitest", "build:dev": "vite build --mode dev", "build:staging": "vite build --mode staging", "build:prod": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "author": {"name": "<PERSON>"}, "contributors": [], "repository": {"url": "https://<EMAIL>/ParlemTelecom/Parlem%20WebComponents/_git/parlem-webcomponents-manualactions", "type": "git"}, "bugs": {"url": "https://<EMAIL>/ParlemTelecom/Parlem%20WebComponents/_git/parlem-webcomponents-manualactions"}, "dependencies": {"@azure/msal-browser": "^4.13.1", "@ag-grid-community/styles": "^32.2.2", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^3.0.3", "@intlify/unplugin-vue-i18n": "^0.10.0", "@vuelidate/core": "^2.0.2", "@vuelidate/validators": "^2.0.2", "ag-grid-community": "^32.3.0", "ag-grid-vue3": "^32.3.0", "axios": "^1.7.4", "moment": "^2.29.4", "parlem-webcomponents-common": "^1.0.225", "postcss-nesting": "^11.2.2", "pinia": "^2.1.7", "vue": "^3.4.29", "vue-chartjs": "^5.3.1", "vue-i18n": "^9.14.0", "vue-toast-notification": "^3.1.3"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@types/jsdom": "^21.1.1", "@types/node": "^18.16.8", "@vitejs/plugin-vue": "^4.2.2", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/test-utils": "^2.3.2", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.14", "eslint": "^8.40.0", "eslint-plugin-vue": "^9.12.0", "jsdom": "^21.1.2", "npm-run-all": "^4.1.5", "postcss": "^8.4.23", "prettier": "^2.8.8", "sass": "^1.62.1", "tailwindcss": "^3.3.2", "typescript": "~4.8.4", "vite": "^4.3.5", "vitest": "^0.29.8", "vue-tsc": "^1.6.4"}, "peerDependencies": {"postcss": "^8.4.21"}}