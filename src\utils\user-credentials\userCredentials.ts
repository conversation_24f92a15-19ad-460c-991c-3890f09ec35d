import { useAuthStore } from '@/store/auth'
import type { IAccount } from '@/store/auth/interfaces/account.interface'
import { checkUserCredentialsMsal } from 'parlem-webcomponents-common'
import type { IPublicClientApplication } from '@azure/msal-browser'

export default async function checkUserCredentials() {
  const authStore = useAuthStore()

  authStore.setAuthenticationError(false)

  try {
    let msalInstance: IPublicClientApplication | undefined = undefined

    msalInstance = (window as any).msalInstance ?? authStore.msalInstance

    const msalResponse = await checkUserCredentialsMsal(msalInstance)
    const accessTokenResponse = msalResponse.result

    authStore.setMsalInstance(msalResponse.msalInstance)

    if (accessTokenResponse && accessTokenResponse.idToken) {
      const accessToken: string = accessTokenResponse.idToken
      const account: IAccount = accessTokenResponse.account

      authStore.setAccessToken(accessToken)
      authStore.setAccount(account)
    } else {
      authStore.setAuthenticationError(true)
    }
  } catch (error) {
    console.error(`Error during authentication: ${error}`)
    authStore.setAuthenticationError(true)
  }
}
