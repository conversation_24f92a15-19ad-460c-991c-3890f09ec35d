/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./public/**/*.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  darkMode: "media", // or 'media' or 'class'
  theme: {
    extend: {
      colors: {
        primary: "rgb(var(--color-primary) / <alpha-value>)",
        secondary: "rgb(var(--color-secondary) / <alpha-value>)",
        "primary-light": "rgb(var(--color-primary-light) / <alpha-value>)",
        "gray-light": "#E1E1E1",
        white: "#ffffff",
        black: "#000000",
        error: "#9B1C1C",
        warning: "#C27803",
        success: "#0E9F6E",
        info: "#6B7280",
        gray: "#c1c1c1",
        //Dark colors
        dark: "#181818",
        "dark-gray-light": "#5B5F62",
        "dark-gray": "#3c4043",
        scoring: {
          text: {
            aaa: "#008a42",
            aa: "#b9ca2b",
            a: "#eecb00",
            b: "#f5b700",
            bb: "#ea7023",
            bbb: "#e20b2a",
          },
          bg: {
            aaa: "rgba(0, 138, 66, 0.3)",
            aa: "rgba(185, 202, 43, 0.3)",
            a: "rgba(238, 203, 0, 0.3)",
            b: "rgba(245, 183, 0, 0.3)",
            bb: "rgba(234, 112, 35, 0.3)",
            bbb: "rgba(226, 11, 42, 0.3)",
          }
        }
      },
    },
  },
  safelist: [
    {
      pattern: /bg-scoring-bg-(aaa|aa|a|b|bb|bbb)/,
      variants: ['hover', 'focus'],
    },
    {
      pattern: /text-scoring-text-(aaa|aa|a|b|bb|bbb)/,
      variants: ['hover', 'focus'],
    }
  ],
  variants: {
    extend: {},
  },
  plugins: [],
};