{
  "home": {
    "header": "Benvingut a Vue 3 I18n ¡!"
  },
  "actions": {
    "start": "Iniciar acció",
    "cancel": {
      "start": "Iniciar Cancel·lació",
      "completed": "Cancel·lat",
      "confirmTitle": "Confirmació de cancel·lació",
      "confirmDescription": "Estàs segur que vols cancel·lar aquest element?",
      "successMessage": "S'ha cancel·lat correctament",
      "errorMessage": "Error en cancel·lar",
      "unexpectedError": "Error inesperat en cancel·lar"
    },
    "migrate": {
      "start": "Iniciar Migració",
      "completed": "Migrat",
      "confirmTitle": "Confirmació de migració",
      "confirmDescription": "Estàs segur que vols migrar aquest element?",
      "successMessage": "S'ha migrat correctament",
      "errorMessage": "Error en migrar",
      "unexpectedError": "Error inesperat en migrar"
    },
    "close": {
      "start": "Iniciar Baixa",
      "completed": "Donat de baixa",
      "confirmTitle": "Confirmació de baixa",
      "confirmDescription": "Estàs segur que vols donar de baixa aquest element?",
      "successMessage": "S'ha donat de baixa correctament",
      "errorMessage": "Error en donar de baixa",
      "unexpectedError": "Error inesperat en donar de baixa"
    }
  },
  "errors": {
    "noResults": "No s'han trobat resultats",
  },
  "pagination": {
    "listCount": "A la llista hi ha {count} {elements}",
    "element": "element",
    "elements": "elements",
    "previousPage": "Pàgina anterior",
    "nextPage": "Pàgina següent"
  },
  "columns": {
    "id": "Id",
    "state": "Estat",
    "actions": "Accions",
    "creationDate": "Data de creació",
    "executionDate": "Data d'execució",
    "rate": "Tarifa",
    "products": "Productes",
    "price": "Preu",
    "type": "Tipus",
    "provider": "Proveïdor",
    "customer": "Client",
    "product": "Producte",
    "fiberNumber": "Número de Fibra",
    "user": "Usuari",
    "customerName": "Nom del client",
    "documentNumber": "Document",
    "contactPhone": "Telèfon de contacte"
  },
  "filters": {
    "documentNumber": "Número de document",
    "provider": "Proveïdor",
    "state": "Estat",
    "manualActionType": "Tipus d'acció Manual",
    "productClass": "Classe de producte",
    "productSubclass": "Subclasse de producte",
    "status": "Estat",
    "subscriptionId": "Id provisió",
    "workOrder": "Ordre de treball"
  },
  "popup": {
    "stateLabel": "Estat",
    "statePlaceholder": "Seleccioneu un estat",
    "reasonLabel": "Motiu",
    "reasonPlaceholder": "Especifiqueu el motiu",
    "cancelButton": "No, cancel·lar",
    "acceptButton": "Sí, accepto"
  }
}
