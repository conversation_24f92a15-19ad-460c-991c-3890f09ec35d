<template>
  <div
    class="flex flex-col bg-white dark:bg-dark-gray-light dark:text-white mb-4 rounded-md shadow-[0_3px_10px_-1px_rgb(0,0,0,0.15)]"
  >
    <div class="flex justify-between items-center p-2 md:p-2 lg:p-2">
      <div class="flex gap-2 items-center ml-3">
        <div class="rounded-full size-8 bg-primary-light flex justify-center items-center">
          <font-awesome-icon :icon="`fa fa-${icon}`" class="size-5 text-primary" />
        </div>
        <p class="text-primary dark:text-gray-light font-bold">{{ title }}</p>
      </div>
      <div class="flex items-center">
        <PwTabs
          v-if="!disableButton"
          :tabs="tabActions.map((tab: any) => ({ ...tab, disabled: tab.disabled || disableAllTabs }))"
          :defaultActiveIndex="'All'"
          @tab-click="isVisible = true"
          class="flex"
          theme="dark:bg-dark-gray-light dark:text-white"
        ></PwTabs>
        <PwPopup
          v-if="isVisible"
          @close="isVisible = false"
          @cancel="isVisible = false"
          @accept="emitAction"
        >
          <div
            class="px-4 py-12 max-w-[500px] mx-auto text-center flex flex-col items-center gap-4"
          >
            <font-awesome-icon
              icon="triangle-exclamation"
              class="w-20 h-20 text-primary dark:text-white"
            />
            <p class="text-2xl leading-6 font-medium text-gray-900 dark:text-white">
              Estàs segur que vols validar l'última venda d’aquest client amb l’scoring actual?
            </p>
          </div>
        </PwPopup>
      </div>
    </div>
    <hr class="col-span-full border-t border-gray-300 dark:border-gray-light" />
    <div>
      <slot></slot>
    </div>
  </div>
</template>
<script setup lang="ts">
import { PwPopup, PwTabs } from 'parlem-webcomponents-common'
import { ref } from 'vue'

interface ITab {
  label: string
  value: string | undefined
  disabled?: boolean
  active?: boolean
  icon?: string
}

const props = withDefaults(
  defineProps<{
    icon?: string
    title: string
    disableButton?: boolean
    tabActions?: ITab[]
    disableAllTabs?: boolean
    handleTabClick?: any
  }>(),
  {
    title: 'TITLE',
    disableButton: false,
    tabActions: () => [{ label: 'Button', value: undefined, disabled: false, active: true }],
    disableAllTabs: false,
    handleTabClick: () => {}
  }
)
const isVisible = ref<any>(false)
const emit = defineEmits(['action'])

const emitAction = () => {
  emit('action')
  isVisible.value = false
}
</script>
