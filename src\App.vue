<template>
  <!-- close -->
  <div class="bg-primary-light w-full p-2">
    <p class="text-primary font-bold text-lg">CLOSE WEBCOMPONENT</p>
  </div>
  <div class="p-4" ref="pwManualActionsClose"></div>

  <!-- cancel -->
  <div class="bg-primary-light w-full p-2">
    <p class="text-primary font-bold text-lg">CANCEL WEBCOMPONENT</p>
  </div>
  <div class="p-4" ref="pwManualActionsCancel"></div>

  <!-- migrate -->
  <div class="bg-primary-light w-full p-2">
    <p class="text-primary font-bold text-lg">MIGRATE WEBCOMPONENT</p>
  </div>
  <div class="p-4" ref="pwManualActionsMigrate"></div>

  <!-- scoring -->
  <div class="bg-primary-light w-full p-2 mt-4">
    <p class="text-primary font-bold text-lg">SCORING WEBCOMPONENT</p>
  </div>
  <div class="p-4" ref="pwManualActionsScoring"></div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { defineUserTheme } from 'parlem-webcomponents-common'

export default defineComponent({
  name: 'app-manual-actions'
})
</script>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import createScript from '@/utils/createScript'

const pwManualActionsClose: any = ref(null)
const pwManualActionsCancel: any = ref(null)
const pwManualActionsMigrate: any = ref(null)
const pwManualActionsScoring: any = ref(null)

const lang = 'ca'
const company = 'Parlem'

onMounted(() => {
  defineUserTheme()

  const close: HTMLElement = document.createElement('pw-manual-actions-general')
  close.setAttribute('lang', lang)
  close.setAttribute('company', company)
  close.setAttribute('type', 'close')
  if (pwManualActionsClose.value) {
    pwManualActionsClose.value.appendChild(close)
  }

  const cancel: HTMLElement = document.createElement('pw-manual-actions-general')
  cancel.setAttribute('lang', lang)
  cancel.setAttribute('company', company)
  cancel.setAttribute('type', 'cancel')
  if (pwManualActionsCancel.value) {
    pwManualActionsCancel.value.appendChild(cancel)
  }

  const migrate: HTMLElement = document.createElement('pw-manual-actions-general')
  migrate.setAttribute('lang', lang)
  migrate.setAttribute('company', company)
  migrate.setAttribute('type', 'migrate')
  if (pwManualActionsMigrate.value) {
    pwManualActionsMigrate.value.appendChild(migrate)
  }

  const scoring: HTMLElement = document.createElement('pw-manual-actions-scoring')
  scoring.setAttribute('lang', lang)
  scoring.setAttribute('company', company)
  scoring.setAttribute('document-number', '38458848L')
  scoring.setAttribute('isManualActions', '')
  if (pwManualActionsScoring.value) {
    pwManualActionsScoring.value.appendChild(scoring)
  }
  
  createScript('VITE_SELLERSELLS_WC', 'sellersells')
})
</script>

<style>
@import '@/assets/styles/index';
@import '~/parlem-webcomponents-common/dist/parlem-webcomponents-common.css';
</style>
