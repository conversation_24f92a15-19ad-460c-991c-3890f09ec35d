<template>
  <div v-if="errorScoring || errorSales">
    <div
      class="flex flex-col justify-center items-center my-8 py-24 bg-white dark:bg-dark rounded-md shadow-[0_3px_10px_-1px_rgb(0,0,0,0.15)]"
    >
      <p class="text-4xl dark:text-white">El client <span class="font-bold">{{ props.documentNumber }}</span> no té scoring</p>
      <p class="text-primary mt-4 text-4xl dark:text-white">Prova de cercar de nou!</p>
      <p class="text-xs text-right text-black dark:text-white mt-4">
        *Només es pot verificar l'scoring d'un client amb el DNI
      </p>
    </div>
  </div>
  <div v-else class="bg-white dark:bg-dark dark:text-white">
    <CustomerScoring
      :scoringData="scoringData"
      :loading="loadingScoring"
      :error="errorScoring"
      :validationResult="validationResult"
      @validate="handleValidate"
    />
    <OrderSummary :sellingDetails="sellingDetails" :loading="loadingSales" :error="errorSales" />
    <CustomerSales :company="company" :documentNumber="documentNumber" isManualActions />
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useManualActions } from '@/store/manual-actions'
import CustomerScoring from './CustomerScoring.vue'
import OrderSummary from './OrderSummary.vue'
import CustomerSales from './CustomerSales.vue'
import { toastUtils } from '@/utils/toast/toast'

import type { IResultItemScoring } from '@/services/api/scoring/interfaces/scoring-value.interface'
import type { IManualActionsStore } from '@/store/manual-actions/interfaces/store.interface'
import type { IResultItemSelling } from '@/services/api/selling/interfaces/selling-value.interface'

const props = defineProps({
  lang: {
    type: String,
    default: 'ca'
  },
  userInfo: {
    type: String,
    default: null
  },
  company: {
    type: String,
    default: null
  },
  documentNumber: {
    type: String,
    required: true
  },
  isManualActions: {
    type: Boolean
  }
})

const scoringData = ref<IResultItemScoring>()
const sellingDetails = ref<IResultItemSelling>()

const loadingScoring = ref(false)
const loadingSales = ref(false)
const errorScoring = ref<Error | null | undefined>(null)
const errorSales = ref<Error | null | undefined>(null)
const sellID = ref<string>()
const validationResult = ref<boolean | null>(null)

const manualActionsStore: IManualActionsStore = useManualActions()

const fetchCustomerScoring = async () => {
  try {
    loadingScoring.value = true
    errorScoring.value = null
    scoringData.value = await manualActionsStore.fetchCustomerScoring(props.documentNumber)

    return scoringData.value
  } catch (error) {
    errorScoring.value = error instanceof Error ? error : new Error(String(error))
  } finally {
    loadingScoring.value = false
  }
}

const fetchSellingDetails = async (sellId: string) => {
  try {
    loadingSales.value = true
    errorSales.value = null
    sellID.value = sellId
    sellingDetails.value = await manualActionsStore.fetchCustomerSellingsDetail(sellId)

    return sellingDetails.value
  } catch (err) {
    errorSales.value = err instanceof Error ? err : new Error(String(err))
  } finally {
    loadingSales.value = false
  }
}

const fetchData = async () => {
  try {
    const scoring = await fetchCustomerScoring()

    if (scoring?.sellId) {
      await fetchSellingDetails(scoring.sellId)
    }
  } catch (err) {
    if (err instanceof Error) {
      throw err
    } else {
      toastUtils.showToast('error', String(err))
    }
  }
}

const handleValidate = async () => {
  try {
    const response = await manualActionsStore.fetchSalesValidation(sellID)
    validationResult.value = response?.isSuccess ?? false

    if (response?.isSuccess) {
      const result = Array.isArray(response.result) ? response.result[0] : response.result
      scoringData.value = result
    }

    toastUtils.showToast('success', 'S’ha validat correctament l’última venda.')
  } catch (error) {
    toastUtils.showToast('error', 'No s’ha pogut validar l’última venda.')
    validationResult.value = false
    throw error
  }
}

onMounted(() => {
  fetchData()
})
</script>
