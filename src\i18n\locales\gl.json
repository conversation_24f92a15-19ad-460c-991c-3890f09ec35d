{"home": {"header": "Benvido a Vue 3 I18n!"}, "actions": {"start": "Iniciar acci<PERSON>", "cancel": {"start": "Iniciar Can<PERSON>ac<PERSON>", "completed": "Cancelado", "confirmTitle": "Confirmación de cancelación", "confirmDescription": "Estás seguro de que queres cancelar este elemento?", "successMessage": "Cancelouse correctamente", "errorMessage": "Erro ao cancelar", "unexpectedError": "Erro inesperado ao cancelar"}, "migrate": {"start": "In<PERSON><PERSON>", "completed": "Migrado", "confirmTitle": "Confirmación de migración", "confirmDescription": "Estás seguro de que queres migrar este elemento?", "successMessage": "Migrouse correctamente", "errorMessage": "<PERSON>rro ao migrar", "unexpectedError": "<PERSON>rro inesperado ao migrar"}, "close": {"start": "In<PERSON><PERSON>", "completed": "<PERSON><PERSON> bai<PERSON>", "confirmTitle": "Confirmación de baixa", "confirmDescription": "Estás seguro de que queres dar de baixa este elemento?", "successMessage": "Deuse de baixa correctamente", "errorMessage": "Erro ao dar de baixa", "unexpectedError": "Erro inesperado ao dar de baixa"}}, "errors": {"noResults": "Non se atoparon resultados"}, "pagination": {"listCount": "Na lista hai {count} {elements}", "element": "elemento", "elements": "elementos", "previousPage": "Páxina anterior", "nextPage": "<PERSON><PERSON><PERSON><PERSON>"}, "columns": {"id": "Id", "state": "Estado", "actions": "Accións", "creationDate": "Data de creación", "executionDate": "Data de execución", "rate": "<PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON>", "type": "Tipo", "provider": "<PERSON><PERSON><PERSON>", "customer": "Cliente", "product": "Produ<PERSON>", "fiberNumber": "Número de Fibra", "user": "Usuario", "customerName": "Nome do cliente", "documentNumber": "Documento", "contactPhone": "Teléfono de contacto"}, "filters": {"documentNumber": "Número de documento", "provider": "<PERSON><PERSON><PERSON>", "state": "Estado", "manualActionType": "Tipo de acción Manual", "productClass": "Clase de produto", "productSubclass": "Subclase de produto", "status": "Estado", "subscriptionId": "Id provisión", "workOrder": "Orde de t<PERSON>allo"}, "popup": {"stateLabel": "Estado", "statePlaceholder": "Seleccione un estado", "reasonLabel": "Motivo", "reasonPlaceholder": "Especifique o motivo", "cancelButton": "Non, cancelar", "acceptButton": "Si, acepto"}}