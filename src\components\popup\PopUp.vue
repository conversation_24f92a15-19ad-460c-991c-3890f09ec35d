<template>
  <PwPopup
    v-if="popupState?.isVisible"
    :close-button="true"
    :cancel-button="false"
    :accept-button="false"
    @close="handleClose"
  >
    <div class="pt-10 flex pb-4 dark:bg-dark flex-col justify-between w-full h-full px-4">
      <div class="flex flex-col items-center pb-6">
        <h3 class="text-2xl font-bold text-black dark:text-white">
          {{ $t(`actions.${mappedType}.confirmTitle`) }}
        </h3>
        <p class="text-black dark:text-white text-center mt-4">
          {{ $t(`actions.${mappedType}.confirmDescription`) }}
        </p>
      </div>

      <div class="flex flex-col gap-4 mb-16">
        <PwSelectAutocomplete
          :placeholder="$t('popup.statePlaceholder')"
          item-title="label"
          item-value="value"
          name="state"
          :label="$t('popup.stateLabel')"
          :items="stateOptions"
          :model-value="selectedState"
          :required="true"
          @update:model-value="handleUpdateState"
          :class="{ dark: isDark }"
        />

        <PwInputText
          v-if="isErrorOrCancelled"
          :label="$t('popup.reasonLabel')"
          :placeholder="$t('popup.reasonPlaceholder')"
          name="reason"
          :model-value="reasonText"
          :required="true"
          @update:model-value="handleUpdateReason"
          :class="{ dark: isDark }"
        />
      </div>

      <div class="flex mt-10">
        <PwButton
          :text="$t('popup.cancelButton')"
          theme="outline-primary"
          class="!my-0 w-full mr-2"
          @click="handleClose"
        />
        <PwButton
          :text="$t('popup.acceptButton')"
          theme="primary"
          class="!my-0 w-full ml-2"
          @click="handleConfirm"
          :disabled="!isFormValid"
        />
      </div>
    </div>
  </PwPopup>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { PwPopup, PwButton, PwSelectAutocomplete, PwInputText } from 'parlem-webcomponents-common'
import { useManualActions } from '@/store/manual-actions'
import { useAuthStore } from '@/store/auth'
import { toastUtils } from '../../utils/toast/toast'
import { getReasonValueByType } from '../../utils/manualactions/getReasonValueByType'
import { getTypeKeyByValue } from '../../utils/manualactions/getTypeKeyByValue'

const { t } = useI18n()
const isDark = ref(localStorage.theme === 'dark')
const manualActionsStore = useManualActions()
const authStore = useAuthStore()
const popupState = computed(() => manualActionsStore.popup)

const username = computed(() => {
  if (authStore.userInfo && authStore.userInfo.username) {
    return authStore.userInfo.username
  }
  return authStore.getAccountUsername || ''
})

const selectedState = ref<any>(null)
const reasonText = ref<string>(popupState.value?.rowData?.executionReason || '')

onMounted(() => {
  if (popupState.value?.rowData?.state) {
    const currentState = stateOptions.value.find(
      (option) => option.value === popupState.value.rowData.state
    )
    if (currentState) {
      selectedState.value = currentState
    }
  }
})

const mappedType = computed(() => {
  if (!popupState.value?.type) return ''
  return getTypeKeyByValue(popupState.value.type) || popupState.value.type
})

const stateOptions = computed(() =>
  manualActionsStore.getPicklistManualActionByName('ManualActionState')
)

const typeReason = computed(() =>
  popupState.value?.type ? getReasonValueByType(popupState.value.type) : null
)

const isErrorOrCancelled = computed(
  () =>
    selectedState.value?.value === 'Error' ||
    selectedState.value?.value === 'Cancelled' ||
    popupState.value?.rowData?.state === 'Error' ||
    popupState.value?.rowData?.state === 'Cancelled'
)

const isFormValid = computed(() => {
  if (!selectedState.value) return false
  if (isErrorOrCancelled.value && !reasonText.value) return false
  return true
})

function handleClose() {
  manualActionsStore.hidePopup()
  selectedState.value = null
  reasonText.value = ''
}

const handleUpdateState = (val: any) => {
  selectedState.value = val
}

const handleUpdateReason = (val: string) => {
  reasonText.value = val
}

const handleConfirm = async () => {
  if (!popupState.value?.rowData || !popupState.value?.type) return

  try {
    const type = getTypeKeyByValue(popupState.value.type) || ''
    const payload = {
      reason: typeReason.value,
      state: selectedState.value.value,
      username: username.value
    }

    if (isErrorOrCancelled.value) {
      payload.reason = reasonText.value
    }

    const response = await manualActionsStore.executeManualAction(
      popupState.value.rowData.id,
      payload,
      type
    )

    if (response?.isSuccess) {
      toastUtils.showToast('success', t(`actions.${mappedType.value}.successMessage`))
    } else {
      toastUtils.showToast('error', t(`actions.${mappedType.value}.errorMessage`))
    }
  } catch (error) {
    console.log(error)
    toastUtils.showToast('error', t(`actions.${mappedType.value}.unexpectedError`))
  } finally {
    handleClose()
  }
}
</script>
