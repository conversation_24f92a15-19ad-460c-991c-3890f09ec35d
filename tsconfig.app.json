{"extends": "@vue/tsconfig/tsconfig.web.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "src/i18n/locales/en.json", "src/i18n/locales/es.json", "src/i18n/locales/ca.json", "src/i18n/locales/gl.json", "global.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "verbatimModuleSyntax": true, "ignoreDeprecations": "5.0"}}